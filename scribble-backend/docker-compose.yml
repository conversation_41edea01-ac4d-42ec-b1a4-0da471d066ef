version: "3.8"

services:
  mongo1:
    image: mongo:latest
    container_name: mongo1
    restart: always
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=admin
    command: >
      mongod --replSet rs0 --bind_ip_all --port 27017
    volumes:
      - mongo1_data:/data/db
    networks:
      - mongo-network

  mongo2:
    image: mongo:latest
    container_name: mongo2
    restart: always
    ports:
      - "27018:27017"
    environment:
      - MONGO_INITDB_DATABASE=admin
    command: >
      mongod --replSet rs0 --bind_ip_all --port 27017
    volumes:
      - mongo2_data:/data/db
    networks:
      - mongo-network

  mongo3:
    image: mongo:latest
    container_name: mongo3
    restart: always
    ports:
      - "27019:27017"
    environment:
      - MONGO_INITDB_DATABASE=admin
    command: >
      mongod --replSet rs0 --bind_ip_all --port 27017
    volumes:
      - mongo3_data:/data/db
    networks:
      - mongo-network

  mongo-setup:
    image: mongo:latest
    container_name: mongo-setup
    depends_on:
      - mongo1
      - mongo2
      - mongo3
    networks:
      - mongo-network
    volumes:
      - ./scripts:/scripts
    entrypoint: >
      bash -c "
        echo 'Waiting for MongoDB instances to be ready...'
        sleep 15
        echo 'Initializing replica set...'
        mongosh --host mongo1:27017 --eval '
          rs.initiate({
            _id: \"rs0\",
            members: [
              { _id: 0, host: \"mongo1:27017\", priority: 2 },
              { _id: 1, host: \"mongo2:27017\", priority: 1 },
              { _id: 2, host: \"mongo3:27017\", priority: 1 }
            ]
          })
        '
        echo 'Waiting for replica set to be ready...'
        sleep 10
        echo 'Replica set initialization complete!'
      "
    restart: "no"

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - mongo-network

networks:
  mongo-network:
    driver: bridge

volumes:
  mongo1_data:
  mongo2_data:
  mongo3_data:
  redis_data: